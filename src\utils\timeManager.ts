import type { GameTime, DailyActivities } from '@/types/game'
import { TimePeriod } from '@/types/game'

// 游戏开始日期：2025年6月1日（周日）
const GAME_START_DATE = new Date(2025, 5, 1) // 月份从0开始，所以5代表6月

// 时间管理器类
export class TimeManager {
  private static instance: TimeManager
  private gameTime: GameTime
  private dailyActivities: DailyActivities
  private listeners: Array<(time: GameTime) => void> = []
  private gameStartDate: Date

  constructor(initialTime?: GameTime) {
    this.gameStartDate = GAME_START_DATE
    this.gameTime = initialTime || this.createDefaultGameTime()

    this.dailyActivities = {
      meal: false,
      bath: false
    }
  }

  static getInstance(initialTime?: GameTime): TimeManager {
    if (!TimeManager.instance) {
      TimeManager.instance = new TimeManager(initialTime)
    }
    return TimeManager.instance
  }

  // 创建默认游戏时间（2025年6月1日上午）
  private createDefaultGameTime(): GameTime {
    return {
      day: 1,
      period: TimePeriod.MORNING,
      dayOfWeek: 7, // 2025年6月1日是周日
      totalDays: 1
    }
  }

  // 获取当前游戏时间
  getCurrentTime(): GameTime {
    return { ...this.gameTime }
  }

  // 获取当前时间段
  getCurrentPeriod(): TimePeriod {
    return this.gameTime.period
  }

  // 获取当前日期
  getCurrentDay(): number {
    return this.gameTime.day
  }

  // 获取星期几
  getDayOfWeek(): number {
    return this.gameTime.dayOfWeek
  }

  // 获取总天数
  getTotalDays(): number {
    return this.gameTime.totalDays
  }

  // 获取每日活动状态
  getDailyActivities(): DailyActivities {
    return { ...this.dailyActivities }
  }

  // 检查是否可以进行某项活动
  canDoActivity(activity: keyof DailyActivities): boolean {
    return !this.dailyActivities[activity]
  }

  // 标记活动已完成
  markActivityDone(activity: keyof DailyActivities): void {
    this.dailyActivities[activity] = true
  }

  // 推进到下一个时间段
  advanceTime(): GameTime {
    switch (this.gameTime.period) {
      case TimePeriod.MORNING:
        this.gameTime.period = TimePeriod.AFTERNOON
        break
      case TimePeriod.AFTERNOON:
        this.gameTime.period = TimePeriod.EVENING
        break
      case TimePeriod.EVENING:
        this.advanceToNextDay()
        break
    }

    this.notifyListeners()
    return this.getCurrentTime()
  }

  // 推进到下一天
  private advanceToNextDay(): void {
    this.gameTime.day += 1
    this.gameTime.totalDays += 1
    this.gameTime.period = TimePeriod.MORNING

    // 更新星期几 (1-7, 周一到周日)
    this.gameTime.dayOfWeek = (this.gameTime.dayOfWeek % 7) + 1

    // 重置每日活动
    this.resetDailyActivities()
  }

  // 重置每日活动状态
  private resetDailyActivities(): void {
    this.dailyActivities = {
      meal: false,
      bath: false
    }
  }

  // 强制推进到指定时间段
  setTimePeriod(period: TimePeriod): GameTime {
    this.gameTime.period = period
    this.notifyListeners()
    return this.getCurrentTime()
  }

  // 强制推进到指定日期
  setDay(day: number): GameTime {
    const dayDiff = day - this.gameTime.day
    this.gameTime.day = day
    this.gameTime.totalDays += dayDiff

    // 重新计算星期几
    this.gameTime.dayOfWeek = ((day - 1) % 7) + 1

    this.notifyListeners()
    return this.getCurrentTime()
  }

  // 设置完整的游戏时间
  setGameTime(time: GameTime): void {
    this.gameTime = { ...time }
    this.notifyListeners()
  }

  // 获取时间段显示文本
  getPeriodText(period?: TimePeriod): string {
    const targetPeriod = period || this.gameTime.period
    const texts = {
      [TimePeriod.MORNING]: '上午',
      [TimePeriod.AFTERNOON]: '下午',
      [TimePeriod.EVENING]: '晚间'
    }
    return texts[targetPeriod] || '未知'
  }

  // 获取星期几显示文本
  getDayOfWeekText(dayOfWeek?: number): string {
    const targetDay = dayOfWeek || this.gameTime.dayOfWeek
    const texts = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
    return texts[targetDay] || '未知'
  }

  // 获取完整的时间显示文本
  getTimeDisplayText(): string {
    return `第${this.gameTime.day}天 ${this.getDayOfWeekText()} ${this.getPeriodText()}`
  }

  // 获取详细的时间显示文本（包含真实日期）
  getDetailedTimeDisplayText(): string {
    const realDate = this.getRealDate()
    const dateStr = `${realDate.getFullYear()}年${realDate.getMonth() + 1}月${realDate.getDate()}日`
    return `${dateStr} ${this.getDayOfWeekText()} ${this.getPeriodText()} (第${this.gameTime.day}天)`
  }

  // 获取对应的真实日期
  getRealDate(): Date {
    const date = new Date(this.gameStartDate)
    date.setDate(date.getDate() + this.gameTime.totalDays - 1)
    return date
  }

  // 获取游戏开始日期
  getGameStartDate(): Date {
    return new Date(this.gameStartDate)
  }

  // 检查是否是特殊日期
  isSpecialDay(): { isSpecial: boolean; type?: string; description?: string } {
    const dayOfWeek = this.gameTime.dayOfWeek

    // 周六是黑市拍卖会
    if (dayOfWeek === 6) {
      return {
        isSpecial: true,
        type: 'black_market',
        description: '黑市拍卖会开放'
      }
    }

    // 周日是休息日
    if (dayOfWeek === 7) {
      return {
        isSpecial: true,
        type: 'rest_day',
        description: '休息日'
      }
    }

    return { isSpecial: false }
  }

  // 检查是否可以访问某个场所
  canAccessLocation(location: string): boolean {
    const period = this.gameTime.period
    const dayOfWeek = this.gameTime.dayOfWeek

    switch (location) {
      case 'workshop':
        return period === TimePeriod.MORNING
      case 'market':
        return period === TimePeriod.AFTERNOON
      case 'garden':
        return period === TimePeriod.AFTERNOON
      case 'farm':
        return period === TimePeriod.AFTERNOON
      case 'black_market':
        return period === TimePeriod.AFTERNOON && dayOfWeek === 6
      case 'bedroom':
        return period === TimePeriod.EVENING
      default:
        return true
    }
  }

  // 添加时间变化监听器
  addTimeListener(listener: (time: GameTime) => void): void {
    this.listeners.push(listener)
  }

  // 移除时间变化监听器
  removeTimeListener(listener: (time: GameTime) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getCurrentTime())
      } catch (error) {
        console.error('Time listener error:', error)
      }
    })
  }

  // 序列化时间数据
  serialize(): { gameTime: GameTime; dailyActivities: DailyActivities } {
    return {
      gameTime: this.getCurrentTime(),
      dailyActivities: this.getDailyActivities()
    }
  }

  // 反序列化时间数据
  deserialize(data: { gameTime: GameTime; dailyActivities: DailyActivities }): void {
    this.gameTime = { ...data.gameTime }
    this.dailyActivities = { ...data.dailyActivities }
    this.notifyListeners()
  }

  // 重置时间管理器
  reset(): void {
    this.gameTime = this.createDefaultGameTime()
    this.resetDailyActivities()
    this.notifyListeners()
  }

  // 销毁实例
  destroy(): void {
    this.listeners = []
    TimeManager.instance = null as any
  }
}

// 创建全局时间管理器实例
export const timeManager = TimeManager.getInstance()

// 时间相关的工具函数
export const timeUtils = {
  // 格式化游戏时间
  formatGameTime(time: GameTime): string {
    return timeManager.getTimeDisplayText()
  },

  // 检查是否是工作时间
  isWorkTime(time: GameTime): boolean {
    return time.period === TimePeriod.MORNING
  },

  // 检查是否是活动时间
  isActivityTime(time: GameTime): boolean {
    return time.period === TimePeriod.AFTERNOON
  },

  // 检查是否是休息时间
  isRestTime(time: GameTime): boolean {
    return time.period === TimePeriod.EVENING
  },

  // 计算两个时间之间的差值（以小时为单位）
  getTimeDifference(time1: GameTime, time2: GameTime): number {
    const dayDiff = time2.totalDays - time1.totalDays
    const periodDiff = time2.period - time1.period
    return dayDiff * 3 + periodDiff // 每天3个时间段
  }
}
