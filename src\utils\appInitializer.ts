import { gameStore } from '@/stores/gameStore'
import { saveManager } from '@/services/saveManager'
import router from '@/router'

/**
 * 应用初始化器
 * 负责管理应用的初始化流程，包括数据库初始化、存档检查、新手引导等
 */
export class AppInitializer {
  private static instance: AppInitializer
  private isInitialized = false

  static getInstance(): AppInitializer {
    if (!AppInitializer.instance) {
      AppInitializer.instance = new AppInitializer()
    }
    return AppInitializer.instance
  }

  /**
   * 初始化应用
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      console.log('开始初始化应用...')

      // 1. 初始化数据库
      await this.initializeDatabase()

      // 2. 检查游戏状态并决定流程
      await this.checkGameStateAndRoute()

      this.isInitialized = true
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    try {
      await gameStore.initializeGame()
      console.log('数据库初始化成功')
    } catch (error) {
      console.error('数据库初始化失败:', error)
      throw new Error('数据库初始化失败')
    }
  }

  /**
   * 检查游戏状态并决定路由
   */
  private async checkGameStateAndRoute(): Promise<void> {
    try {
      // 检查是否完成过新手引导
      const tutorialCompleted = localStorage.getItem('tutorial_completed')
      
      // 检查是否有存档
      const hasSaves = await saveManager.hasSaves()

      if (!tutorialCompleted) {
        // 第一次进入游戏，显示新手引导
        console.log('首次进入游戏，将显示新手引导')
        // 路由会自动跳转到 /game/start，新手引导会在 GameLayout 中自动触发
        return
      }

      if (hasSaves) {
        // 有存档且完成过引导，跳转到开始界面让用户选择
        console.log('发现存档，跳转到开始界面')
        router.push({ name: 'start' })
      } else {
        // 完成过引导但没有存档，直接跳转到开始界面
        console.log('无存档，跳转到开始界面')
        router.push({ name: 'start' })
      }
    } catch (error) {
      console.error('检查游戏状态失败:', error)
      // 出错时跳转到开始界面
      router.push({ name: 'start' })
    }
  }

  /**
   * 重置应用状态（用于测试或重新开始）
   */
  async reset(): Promise<void> {
    try {
      // 清除新手引导标记
      localStorage.removeItem('tutorial_completed')
      
      // 重置游戏状态
      gameStore.resetGameState()
      
      // 清空数据库（可选）
      // await databaseService.clearAllData()
      
      this.isInitialized = false
      console.log('应用状态已重置')
    } catch (error) {
      console.error('重置应用状态失败:', error)
      throw error
    }
  }
}

/**
 * 全局初始化函数
 */
export async function initializeApp(): Promise<void> {
  const initializer = AppInitializer.getInstance()
  await initializer.initialize()
}

/**
 * 重置应用（用于开发和测试）
 */
export async function resetApp(): Promise<void> {
  const initializer = AppInitializer.getInstance()
  await initializer.reset()
}
