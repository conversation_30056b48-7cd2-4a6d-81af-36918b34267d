import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import { initializeAutoSave } from './utils/autoSave'
import { initializeApp } from './utils/appInitializer'
import 'dayjs/locale/zh-cn'
import dayjs from 'dayjs'

// 设置dayjs中文本地化
dayjs.locale('zh-cn')

const app = createApp(App)

app.use(router)
app.use(Antd)

app.mount('#app')

// 初始化应用
initializeApp()

// 初始化自动保存系统
initializeAutoSave()
