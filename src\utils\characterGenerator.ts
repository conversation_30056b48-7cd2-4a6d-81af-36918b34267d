import type { Character } from '@/types/game'
import { Rarity } from '@/types/game'
import { generateId } from '@/utils/gameData'
import NameListData from '@/assets/data/characters/NameList.json'

// 角色模板接口
interface CharacterTemplate {
  nameTemplate: string
  descriptionTemplates: string[]
  attributeRanges: {
    charm: [number, number]
    skill: [number, number]
    stamina: [number, number]
    wisdom: [number, number]
  }
}

// 头像ID列表（模拟从assets/graphics/portraits获取）
const PORTRAIT_IDS = [
  'portrait_001', 'portrait_002', 'portrait_003', 'portrait_004', 'portrait_005',
  'portrait_006', 'portrait_007', 'portrait_008', 'portrait_009', 'portrait_010',
  'portrait_011', 'portrait_012', 'portrait_013', 'portrait_014', 'portrait_015'
]

// 普通角色模板
const NORMAL_TEMPLATES: CharacterTemplate[] = [
  {
    nameTemplate: "小雅",
    descriptionTemplates: [
      "温柔善良的少女，总是面带微笑",
      "性格开朗，喜欢帮助他人",
      "有着一双明亮的眼睛，充满智慧"
    ],
    attributeRanges: {
      charm: [10, 20],
      skill: [8, 18],
      stamina: [12, 22],
      wisdom: [6, 16]
    }
  },
  {
    nameTemplate: "小月",
    descriptionTemplates: [
      "安静内向的少女，喜欢读书",
      "思维敏捷，善于思考问题",
      "虽然话不多，但很有主见"
    ],
    attributeRanges: {
      charm: [8, 16],
      skill: [12, 22],
      stamina: [10, 18],
      wisdom: [15, 25]
    }
  },
  {
    nameTemplate: "小花",
    descriptionTemplates: [
      "活泼可爱的少女，充满活力",
      "喜欢运动和户外活动",
      "总是精力充沛，感染着周围的人"
    ],
    attributeRanges: {
      charm: [12, 20],
      skill: [10, 18],
      stamina: [18, 28],
      wisdom: [8, 16]
    }
  },
  {
    nameTemplate: "小琴",
    descriptionTemplates: [
      "优雅端庄的少女，精通音律",
      "举止优雅，谈吐不凡",
      "有着艺术家的气质和天赋"
    ],
    attributeRanges: {
      charm: [18, 28],
      skill: [15, 25],
      stamina: [8, 16],
      wisdom: [12, 20]
    }
  },
  {
    nameTemplate: "小梅",
    descriptionTemplates: [
      "坚强独立的少女，意志坚定",
      "做事认真负责，从不轻言放弃",
      "虽然外表柔弱，内心却很强大"
    ],
    attributeRanges: {
      charm: [10, 18],
      skill: [14, 24],
      stamina: [16, 24],
      wisdom: [12, 20]
    }
  }
]

// 稀有角色模板
const RARE_TEMPLATES: CharacterTemplate[] = [
  {
    nameTemplate: "紫霞",
    descriptionTemplates: [
      "神秘高贵的少女，身世不凡",
      "拥有着超凡的天赋和能力",
      "气质出众，仿佛来自仙境"
    ],
    attributeRanges: {
      charm: [25, 35],
      skill: [20, 30],
      stamina: [18, 28],
      wisdom: [22, 32]
    }
  },
  {
    nameTemplate: "青莲",
    descriptionTemplates: [
      "清雅脱俗的少女，如莲花般纯洁",
      "心灵手巧，制作技艺精湛",
      "总是保持着内心的宁静"
    ],
    attributeRanges: {
      charm: [20, 30],
      skill: [25, 35],
      stamina: [15, 25],
      wisdom: [20, 30]
    }
  },
  {
    nameTemplate: "红叶",
    descriptionTemplates: [
      "热情如火的少女，充满激情",
      "行动力强，敢于挑战困难",
      "如秋日红叶般绚烂夺目"
    ],
    attributeRanges: {
      charm: [22, 32],
      skill: [18, 28],
      stamina: [25, 35],
      wisdom: [18, 28]
    }
  },
  {
    nameTemplate: "雪儿",
    descriptionTemplates: [
      "冰雪聪明的少女，智慧超群",
      "思维清晰，洞察力敏锐",
      "如雪花般纯洁无瑕"
    ],
    attributeRanges: {
      charm: [18, 28],
      skill: [22, 32],
      stamina: [16, 26],
      wisdom: [28, 38]
    }
  }
]

// 随机数生成器
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

function randomChoice<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

// 生成随机属性值
function generateAttributes(ranges: CharacterTemplate['attributeRanges']) {
  return {
    charm: randomInt(ranges.charm[0], ranges.charm[1]),
    skill: randomInt(ranges.skill[0], ranges.skill[1]),
    stamina: randomInt(ranges.stamina[0], ranges.stamina[1]),
    wisdom: randomInt(ranges.wisdom[0], ranges.wisdom[1])
  }
}

// 从名字列表中随机选择名字
function getRandomCharacterName(): string {
  return randomChoice(NameListData.charactersName)
}

// 生成角色名称变体（保留原有功能作为备用）
function generateCharacterName(template: string): string {
  const suffixes = ['', '儿', '子', '妹', '姐']
  const prefixes = ['', '小', '阿']

  // 30% 概率添加前缀或后缀
  if (Math.random() < 0.3) {
    if (Math.random() < 0.5) {
      return randomChoice(prefixes) + template
    } else {
      return template + randomChoice(suffixes)
    }
  }

  return template
}

// 获取随机头像ID
function getRandomPortraitId(): string {
  return randomChoice(PORTRAIT_IDS)
}

// 生成普通角色
export function generateNormalCharacter(): Character {
  const template = randomChoice(NORMAL_TEMPLATES)
  const name = getRandomCharacterName() // 使用名字列表中的随机名字
  const description = randomChoice(template.descriptionTemplates)
  const attributes = generateAttributes(template.attributeRanges)

  return {
    id: generateId('char_'),
    name,
    description,
    rarity: Rarity.NORMAL,
    portraitId: getRandomPortraitId(),
    level: 1,
    exp: 0,
    maxExp: 100,
    attributes,
    isWorking: false,
    storylineCompleted: false
  }
}

// 生成稀有角色
export function generateRareCharacter(): Character {
  const template = randomChoice(RARE_TEMPLATES)
  const name = getRandomCharacterName() // 使用名字列表中的随机名字
  const description = randomChoice(template.descriptionTemplates)
  const attributes = generateAttributes(template.attributeRanges)

  return {
    id: generateId('char_'),
    name,
    description,
    rarity: Rarity.RARE,
    portraitId: getRandomPortraitId(),
    level: 1,
    exp: 0,
    maxExp: 100,
    attributes,
    isWorking: false,
    storylineCompleted: false
  }
}

// 根据稀有度生成角色
export function generateCharacterByRarity(rarity: Rarity): Character {
  switch (rarity) {
    case Rarity.NORMAL:
      return generateNormalCharacter()
    case Rarity.RARE:
      return generateRareCharacter()
    default:
      return generateNormalCharacter()
  }
}

// 批量生成角色
export function generateMultipleCharacters(count: number, rarity?: Rarity): Character[] {
  const characters: Character[] = []

  for (let i = 0; i < count; i++) {
    if (rarity) {
      characters.push(generateCharacterByRarity(rarity))
    } else {
      // 随机稀有度：80% 普通，20% 稀有
      const randomRarity = Math.random() < 0.8 ? Rarity.NORMAL : Rarity.RARE
      characters.push(generateCharacterByRarity(randomRarity))
    }
  }

  return characters
}

// 保存生成的角色数据到本地存储
export function saveGeneratedCharacter(character: Character): void {
  const savedCharacters = getGeneratedCharacters()
  savedCharacters.push(character)
  localStorage.setItem('generated_characters', JSON.stringify(savedCharacters))
}

// 获取已生成的角色数据
export function getGeneratedCharacters(): Character[] {
  const saved = localStorage.getItem('generated_characters')
  return saved ? JSON.parse(saved) : []
}

// 清除生成的角色数据
export function clearGeneratedCharacters(): void {
  localStorage.removeItem('generated_characters')
}

// 生成初始角色（新手引导用）
export function generateStarterCharacters(): Character[] {
  const characters: Character[] = []

  // 第一个初始角色 - 平衡型
  const char1: Character = {
    id: generateId(),
    name: getRandomCharacterName(),
    description: '温柔善良的少女，各方面都很均衡，是很好的伙伴。',
    rarity: Rarity.NORMAL,
    portraitId: 'starter_01',
    level: 1,
    exp: 0,
    maxExp: 100,
    attributes: {
      charm: 15,
      skill: 15,
      stamina: 15,
      wisdom: 15
    },
    isWorking: false,
    workLocation: undefined,
    unlockCondition: undefined,
    storylineCompleted: false
  }
  characters.push(char1)

  // 第二个初始角色 - 技艺特长
  const char2: Character = {
    id: generateId(),
    name: getRandomCharacterName(),
    description: '心灵手巧的少女，在制作方面有着出色的天赋。',
    rarity: Rarity.NORMAL,
    portraitId: 'starter_02',
    level: 1,
    exp: 0,
    maxExp: 100,
    attributes: {
      charm: 12,
      skill: 20,
      stamina: 13,
      wisdom: 15
    },
    isWorking: false,
    workLocation: undefined,
    unlockCondition: undefined,
    storylineCompleted: false
  }
  characters.push(char2)

  return characters
}
