<template>
  <div v-if="showTutorial" class="tutorial-overlay">
    <div class="tutorial-content">
      <a-card :title="currentStep.title" class="tutorial-card">
        <template #extra>
          <a-button size="small" @click="skipTutorial">跳过引导</a-button>
        </template>

        <div class="tutorial-body">
          <div class="tutorial-text">
            {{ currentStep.content }}
          </div>

          <div v-if="currentStep.image" class="tutorial-image">
            <img :src="currentStep.image" :alt="currentStep.title" />
          </div>
        </div>

        <template #actions>
          <a-space>
            <a-button v-if="currentStepIndex > 0" @click="previousStep">
              上一步
            </a-button>
            <a-button v-if="currentStepIndex < tutorialSteps.length - 1" type="primary" @click="nextStep">
              下一步 ({{ currentStepIndex + 1 }}/{{ tutorialSteps.length }})
            </a-button>
            <a-button v-else type="primary" @click="completeTutorial">
              完成引导
            </a-button>
          </a-space>
        </template>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { gameStore } from '@/stores/gameStore'

interface TutorialStep {
  id: string
  title: string
  content: string
  image?: string
  route?: string
  action?: () => void
}

const router = useRouter()

const showTutorial = ref(false)
const currentStepIndex = ref(0)

// 新手引导步骤
const tutorialSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: '欢迎来到琼浆玉露',
    content: '欢迎来到这个美妙的世界！在这里，你将经营一家名为"琉璃胭脂坊"的特殊店铺，与各种可爱的少女一起度过美好的时光。',
    action: async () => {
      await gameStore.startTutorialGame()
    }
  },
  {
    id: 'time_system',
    title: '时间系统',
    content: '游戏采用时间段制：上午在琉璃胭脂坊工作，下午可以外出活动，晚间在寝室休息。每个时间段都有不同的活动可以进行。'
  },
  {
    id: 'energy_system',
    title: '体力系统',
    content: '你的体力是有限的，每次行动都会消耗体力。体力耗尽后会自动进入晚间时段。通过用餐、洗澡可以恢复体力，睡觉会完全恢复并进入新的一天。'
  },
  {
    id: 'workshop_intro',
    title: '琉璃胭脂坊',
    content: '这是你的主要经营场所。你可以派遣少女到会客大厅表演，或者到各种工坊制作商品。不同的少女有不同的属性，会影响工作效果。',
    route: 'workshop'
  },
  {
    id: 'characters',
    title: '角色管理',
    content: '查看你拥有的角色，了解她们的属性和等级。角色会通过工作获得经验并升级，属性也会随之提升。',
    route: 'characters'
  },
  {
    id: 'market',
    title: '翡翠商会',
    content: '下午时段可以前往翡翠商会购买材料或出售商品。合理的买卖策略是经营成功的关键。',
    route: 'market'
  },
  {
    id: 'garden',
    title: '孤独园',
    content: '在孤独园可以招募新的少女加入你的团队。普通招募只需要灵石，稀有招募需要特殊材料但能获得更强的角色。',
    route: 'garden'
  },
  {
    id: 'skill_tree',
    title: '发展树',
    content: '通过发展树可以解锁新的工坊、增加派遣槽位或提升角色成长速度。合理规划发展路线很重要。',
    route: 'tree'
  },
  {
    id: 'complete',
    title: '引导完成',
    content: '恭喜你完成了新手引导！现在你可以开始自由探索这个世界了。记住，合理安排时间和资源是成功的关键。祝你游戏愉快！'
  }
]

const currentStep = computed(() => tutorialSteps[currentStepIndex.value])

// 下一步
const nextStep = () => {
  if (currentStepIndex.value < tutorialSteps.length - 1) {
    currentStepIndex.value++

    // 如果有路由，自动跳转
    if (currentStep.value.route) {
      router.push({ name: currentStep.value.route })
    }

    // 如果有动作，执行动作
    if (currentStep.value.action) {
      currentStep.value.action()
    }
  }
}

// 上一步
const previousStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
  }
}

// 跳过引导
const skipTutorial = () => {
  showTutorial.value = false
  localStorage.setItem('tutorial_completed', 'true')
  message.info('已跳过新手引导')
}

// 完成引导
const completeTutorial = () => {
  showTutorial.value = false
  localStorage.setItem('tutorial_completed', 'true')
  message.success('新手引导完成！')

  // 返回地图
  router.push({ name: 'map' })
}

// 开始引导
const startTutorial = () => {
  showTutorial.value = true
  currentStepIndex.value = 0
}

// 暴露方法给父组件
defineExpose({
  startTutorial,
  skipTutorial
})
</script>

<style scoped>
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorial-content {
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.tutorial-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.tutorial-body {
  padding: 16px 0;
}

.tutorial-text {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
  color: #333;
}

.tutorial-image {
  text-align: center;
}

.tutorial-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tutorial-content {
    width: 95%;
    margin: 20px;
  }

  .tutorial-text {
    font-size: 14px;
  }
}
</style>
