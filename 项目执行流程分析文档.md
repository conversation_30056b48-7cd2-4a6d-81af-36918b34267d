# Nectar Game 项目执行流程分析文档

## 项目概述
Nectar Game 是一个基于 Vue3 + Ant Design Vue 的游戏项目，采用 TypeScript 开发，使用 IndexedDB 进行数据持久化。

## 核心架构

### 1. 状态管理层 (src/stores)

#### gameStore.ts - 游戏核心状态管理
**主要状态：**
- `isInitialized`: 游戏初始化状态
- `currentSave`: 当前游戏存档
- `gameTime`: 游戏时间（通过 timeManager 计算属性获取）
- `playerStatus`: 玩家状态（体力、金钱、经验等）
- `characters`: 角色列表
- `inventory`: 库存物品
- `dailyActivities`: 每日活动状态
- `unlockedFeatures`: 解锁功能

**核心方法：**
- `initializeGame()`: 初始化游戏
- `startNewGame()`: 开始新游戏
- `loadGame()`: 加载游戏
- `saveGame()`: 保存游戏
- `advanceGameTimePeriod()`: 推进时间段 ⭐
- `consumeEnergy()`: 消耗体力
- `addCharacter()`: 添加角色
- `addItem()/removeItem()`: 物品管理

#### dispatchStore.ts - 派遣系统管理
**主要状态：**
- `activeTasks`: 当前派遣任务
- `completedTasks`: 已完成任务

**核心方法：**
- `canDispatch()`: 检查是否可派遣
- `dispatchCharacters()`: 派遣角色
- `completeTask()`: 完成任务
- `autoCompleteTask()`: 自动完成任务
- `handleTimePeriodChange()`: 处理时间段变化

#### skillStore.ts - 技能系统管理
**主要状态：**
- `skills`: 技能列表
- `availablePoints`: 可用技能点

**核心方法：**
- `unlockSkill()`: 解锁技能
- `resetSkills()`: 重置技能

#### eventStore.ts - 事件系统管理
**主要状态：**
- `events`: 事件列表
- `tasks`: 任务列表

**核心方法：**
- `addEvent()`: 添加事件
- `addTask()`: 添加任务
- `completeTask()`: 完成任务

### 2. 服务层 (src/services)

#### saveManager.ts - 存档管理服务
**核心功能：**
- `createNewGame()`: 创建新游戏
- `loadGame()`: 加载游戏
- `saveCurrentGame()`: 保存当前游戏
- `getAllSaveInfo()`: 获取所有存档信息
- `deleteSave()`: 删除存档
- `startAutoSave()`: 启动自动保存

#### database.ts - 数据库服务
**核心功能：**
- `init()`: 初始化 IndexedDB
- `saveGame()`: 保存游戏到数据库
- `loadGame()`: 从数据库加载游戏
- `getAllSaves()`: 获取所有存档
- `deleteSave()`: 删除存档

### 3. 工具层 (src/utils)

#### timeManager.ts - 时间管理器 ⭐
**核心功能：**
- `advanceTime()`: 推进到下一时间段
- `setTimePeriod()`: 设置时间段
- `getCurrentTime()`: 获取当前时间
- `getDailyActivities()`: 获取每日活动状态

#### characterGenerator.ts - 角色生成器
**核心功能：**
- `generateNormalCharacter()`: 生成普通角色
- `generateRareCharacter()`: 生成稀有角色
- `generateCharacterName()`: 生成角色名称 ⭐

## 游戏执行流程

### 1. 游戏启动流程
```
启动游戏 → 初始化数据库 → 检查存档 → 选择新游戏/加载存档 → 进入主场景
```

### 2. 时间推进流程 ⭐
```
点击推进时间按钮 → gameStore.advanceGameTimePeriod() → timeManager.advanceTime() → 
更新游戏状态 → 触发时间变化监听器 → 自动保存
```

### 3. 派遣系统流程
```
选择工坊 → 选择角色 → 检查条件 → 创建派遣任务 → 设置角色工作状态 → 
时间推进时自动完成 → 获得奖励
```

## 重复/弃用方法分析

### 1. 时间管理重复
- `gameStore.advanceGameTimePeriod()` 和 `timeManager.advanceTime()` 功能重复
- `utils/gameData.ts` 中的 `advanceGameTime()` 方法已被 timeManager 替代

### 2. 角色生成重复
- `characterGenerator.ts` 中的名称生成逻辑需要改为使用 `NameList.json`

### 3. 数据验证重复
- `saveManager.ts` 和 `dataValidator.ts` 中存在重复的数据验证逻辑

## 问题识别

### 1. 时间推进问题 ⭐
- MapView 中时间推进可能存在状态同步问题
- `canAdvanceTime` 计算属性可能返回 false

### 2. 日历组件问题 ⭐
- CalendarView 使用 dayjs() 而非游戏时间
- 需要与 timeManager 的游戏开始时间同步

### 3. 角色名称生成问题 ⭐
- 当前使用模板生成，需要改为从 NameList.json 随机选择

## 建议优化

1. **统一时间管理**：移除重复的时间推进方法
2. **修复时间推进**：确保 MapView 正确调用时间推进逻辑
3. **同步日历组件**：使用游戏时间而非系统时间
4. **优化角色生成**：使用预定义名称列表
5. **简化数据验证**：合并重复的验证逻辑
